import { Component, <PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NotifResult, RequestType, ResultType, UnviredCordovaSDK, UnviredCredential } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { filter, Observable, switchMap, take, tap, map } from 'rxjs';
import { Store } from '@ngrx/store';
import { selectAllForms, selectAllNotifications, selectAllTemplates, selectPrefilledData, selectRigData, selectRigLoadedFromDb } from 'src/app/store/store.selector';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import * as RigActions from 'src/app/store/store.actions';
import { AlertController, IonButton, IonButtons, IonCol, IonContent, IonGrid, IonHeader, IonItem, IonItemDivider, IonLabel, IonList, IonMenuButton, IonRouterOutlet, IonRow, IonSearchbar, IonThumbnail, IonTitle, IonToggle, IonToolbar, MenuController, Platform } from '@ionic/angular/standalone';
import { DataService } from 'src/app/services/data.service';
import { FORM_HEADER } from 'src/models/FORM_HEADER';
import { CustomDatePipe } from 'src/app/pipes/custom-date.pipe';
import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { UtilityService } from 'src/app/services/utility.service';
import { InAppBrowser, InAppBrowserObject } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { WebView } from '@awesome-cordova-plugins/ionic-webview/ngx';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { AppConstants } from 'src/app/constants/appConstants';
import { FormUtilityService } from 'src/app/services/formUtility.service';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import { FORM_DATA } from 'src/models/FORM_DATA';
import moment from 'moment';
@Component({
  selector: 'app-forms',
  templateUrl: './forms.page.html',
  styleUrls: ['./forms.page.scss'],
  standalone: true,
  imports: [IonRow, IonCol, IonGrid,  IonItemDivider, IonThumbnail, IonSearchbar, IonToggle, TranslateModule, CustomDatePipe, CommonModule, FormsModule ,IonItem ,IonList,IonToolbar , IonLabel,  IonHeader, IonButtons ,IonMenuButton ,IonContent ,IonTitle , IonButton]
})
export class FormsPage implements OnInit {
  notifications$: Observable<NotifResult[]> | undefined;
  templates$: Observable<TEMPLATE_HEADER[]|null> | undefined;
  prefilledData$!: Observable<any>;
  forms$!: Observable<FORM_HEADER[]>;
  groupedForms: { category: string, items: FORM_HEADER[] }[] = [];
  plainList: FORM_HEADER[] = [];
  categoryToggle: boolean = false; // now a simple boolean
  isTemplateLoadTriggered = false;
  searchTermSubject = new BehaviorSubject<string>('');
  filteredForms$: Observable<FORM_HEADER[]> | undefined;
  hideCompletedToggle: boolean = false;
  trustedIndexUrl!: SafeResourceUrl;
  private myBrowser!: InAppBrowserObject;
  lidOfLastOpenedForm: string = '';

  constructor(
    private unviredSdk: UnviredCordovaSDK,
    private store: Store,
    private routerOutlet: IonRouterOutlet,
    private menuCtrl: MenuController,
    private dataService: DataService,
    private file: File,
    private utilityService: UtilityService,
    private iab: InAppBrowser,
    private platform: Platform,
    private webview: WebView,
    private sanitizer: DomSanitizer,
    private ngZone: NgZone,
    private alertController: AlertController,
    private formUtilityService: FormUtilityService,
    private busyIndicatorService: BusyIndicatorService,
    public AppConstants: AppConstants
  ) {
    this.routerOutlet.swipeGesture = false;
    this.menuCtrl.swipeGesture(true)
  }

  ngOnInit() {
    // this.store.subscribe(state => console.log('AppState:', state));


     this.store.dispatch(RigActions.loadRigFromDb());
      
        // When rig data is loaded, check if templates need to be loaded
        this.store.select(selectRigLoadedFromDb).pipe(
          tap((loaded: any) => console.log('Rig data loaded from DB:', loaded)),
          filter((loaded): loaded is boolean => !!loaded),
          switchMap(() => this.store.select(selectRigData).pipe(take(1))), // Get the rig data
          take(1)
        ).subscribe((rigData) => {
          if (!rigData || !rigData.RIG_NO) {
            console.warn('RigData is null or RIG_NO missing — opening site popup');
            // await this.openSiteNumberPopup();
          } else {
            // Dispatch action to load templates only if they're not already loaded
            this.store.dispatch(RigActions.loadAllTemplatesFromDb());
             this.store.dispatch(RigActions.loadPrefilledData());
          }
        });
      
        this.store.select(selectAllNotifications).pipe(
          tap(events => {
            // console.log('Current notifications:', events);
            // Find the first event of type 4
            const firstEventType4 = events.find(event => event.type === 4);
            
            if (firstEventType4 && !this.isTemplateLoadTriggered) {
              // Only dispatch the action if we haven't already triggered the DB call
              console.log('First event with type 4 found:', firstEventType4);
              this.store.dispatch(RigActions.loadAllTemplatesFromDb());
             
              this.store.dispatch(RigActions.loadPrefilledData());
             
                 this.templates$ = this.store.select(selectAllTemplates).pipe(
                   tap(loaded => {
                    
                     console.log('Templates loaded from DB:', loaded);
                    //  this.isLoading = !loaded;  // Set loading state based on the `loaded` flag
                   })
                 )
                 this.isTemplateLoadTriggered = true;

                 this.prefilledData$ = this.store.select(selectPrefilledData);
            }
          })
        ).subscribe();
      
    // this.notifications$ = this.store.select(selectAllNotifications).pipe(distinctUntilChanged());
    
    this.store.select(selectRigLoadedFromDb).pipe(
      tap((loaded: any) => console.log('Rig data loaded from DB:', loaded)),
      filter((loaded): loaded is boolean => !!loaded),
      switchMap(() => this.store.select(selectRigData).pipe(take(1))),
      take(1)
    ).subscribe((rigData) => {
      if (!rigData || !rigData.RIG_NO) {
        console.warn('RigData is null or RIG_NO missing — opening site popup');
        // await this.openSiteNumberPopup();
      } else {
        // Only execute forms logic if rigNo is present
        this.store.dispatch(RigActions.loadAllFormsFromDb());
        this.forms$ = this.store.select(selectAllForms);
        this.forms$.subscribe((forms: FORM_HEADER[]) => {
          this.plainList = forms;
          this.updateGroupedForms();
        });
        this.filteredForms$ = this.searchTermSubject.asObservable().pipe(
          switchMap(term =>
            this.forms$.pipe(
              map(forms =>
                forms.filter(form =>
                  (form.TEMPLATE_DESC?.toLowerCase().includes(term) ||
                   form.FORM_ID?.toLowerCase().includes(term) ||
                   form.CATEGORY_DESC?.toLowerCase().includes(term))
                )
              )
            )
          )
        );
      }
    });
  }

  updateGroupedForms() {
    if (this.categoryToggle) {
      const groups: { [key: string]: FORM_HEADER[] } = {};
      this.plainList.forEach(form => {
        const cat = form.CATEGORY_DESC ? form.CATEGORY_DESC : '';
        if (!groups[cat]) {
          groups[cat] = [];
        }
        groups[cat].push(form);
      });
      this.groupedForms = Object.entries(groups).map(([category, items]) => ({ category, items }));
    } else {
      this.groupedForms = [{ category: 'Forms', items: this.plainList }];
    }
  }

  async getForms(){
    console.log('send log', this.plainList , this.categoryToggle )
    await this.unviredSdk.sendLogToServer()
     this.forms$ = this.store.select(selectAllForms)
     this.forms$.subscribe((forms: FORM_HEADER[]) => {
      this.plainList = forms;
      console.log('this.forms is ' , forms)
      this.updateGroupedForms();
    })
    //  this.categoryToggle$.subscribe(val => {
  // console.log('Toggle value:', val);
    // loading$ = this.store.select(selectFormsLoading);
    // error$ = this.store.select(selectFormsError);
  }
  

  onCategoryToggle(event: any) {
    this.categoryToggle = event.detail.checked;
    this.updateGroupedForms();
  }


   onHideCompletedToggle(event: any) {
    this.hideCompletedToggle = event.detail.checked;
    this.applyFilters();
  }

  applyFilters() {
    let filtered = this.plainList;
    if (this.hideCompletedToggle) {
      filtered = filtered.filter(form => form.FORM_STATUS !== 'SUBM');
    }
    if (this.searchTermSubject) {
      const term = this.searchTermSubject.getValue();
      if (term) {
        filtered = filtered.filter(form =>
          (form.TEMPLATE_DESC?.toLowerCase().includes(term) ||
           form.FORM_ID?.toLowerCase().includes(term) ||
           form.CATEGORY_DESC?.toLowerCase().includes(term))
        );
      }
    }
    this.plainList = filtered;
    this.updateGroupedForms();
  }

    onSearchChange(term: string | null | undefined) {
      const searchValue = term?.toLowerCase() ?? '';
      this.searchTermSubject.next(searchValue);
      if (searchValue === '') {
        // If search is cleared, show all forms based on current filter
        this.forms$.subscribe((forms: FORM_HEADER[]) => {
          let filtered = forms;
          if (this.hideCompletedToggle) {
            filtered = filtered.filter(form => form.FORM_STATUS !== 'SUBM');
          }
          this.plainList = filtered;
          this.updateGroupedForms();
        });
      } else {
        // Otherwise, filter by search term
        this.filteredForms$?.subscribe(filtered => {
          this.plainList = filtered;
          this.updateGroupedForms();
        });
      }
    }

  async onFormSelect(form: FORM_HEADER) {
    console.log('Form selected:', form);
    try {
      const pathResult: any = await this.unviredSdk.getAttachmentFolderPath();
      if (!pathResult) {
        console.error('Could not retrieve attachment folder path.');
        return;
      }
      console.log('pathResult is ' , pathResult);
      const attachmentPath = pathResult;
      const folderName = form.VER_ID;
      const zipFileName = `${form.VER_ID}.zip`;

      console.log('attachmentPath is ' , attachmentPath);
      console.log('folderName is ' , folderName);
      try {
        await this.file.checkDir(attachmentPath, folderName);
        console.log('Folder exists in path.');

        await this.checkForHtmlFileInFolder(attachmentPath, folderName, form);
      } catch (dirError) {
        console.log('Folder does not exist.', dirError);
            console.log(`Folder '${folderName}' does not exist. Creating directory...`);
        try {
          await this.file.createDir(attachmentPath, folderName, false);
          // console.log(`Directory '${folderName}' created in path '${attachmentPath}'.`);
        } catch (createError) {
          this.unviredSdk.logError('FormsPage', 'onFormSelect', `Failed to create directory '${folderName}'`)
        }
        try {
          const attachmentAsArrayBuffer = await this.utilityService.getAttachmentAsArrayBuffer(attachmentPath+'/'+zipFileName)
          await this.utilityService.unzipTheFileAndWriteFileContent(attachmentAsArrayBuffer , folderName , attachmentPath )
             await this.file.checkDir(attachmentPath, folderName);
        console.log('Folder exists in path.');

        await this.checkForHtmlFileInFolder(attachmentPath, folderName, form);

        } catch (zipError) {
          this.unviredSdk.logInfo('FormsPage','onFormSelect',`Neither folder nor ZIP file exists in path ${attachmentPath}.`)
        }
      }
    } catch (error) {
      console.error('Error getting attachment folder path:', error);
    }
  }

  private async updateFormDataInHTML(folderPath: string, form: FORM_HEADER) {
    try {
      // Read the HTML file
      const htmlContent = await this.file.readAsText(folderPath, 'index.html');
      
      // Get prefilled data including logo
      const prefilledData = await this.store.select(selectPrefilledData).pipe(take(1)).toPromise();
      const logo = prefilledData?.LOGO || '';
      
      // Create the script content
      const script = `var prefilledData = ${JSON.stringify(prefilledData || {})};
                      var companyLogo = '${logo}';`;
      
      // Replace the placeholder with the script
      const updatedHtml = htmlContent.replace(
        '<!-- FORM DATA -->',
        `<!-- FORM DATA -->\n<script>\n${script}\n</script>\n<!-- ./FORM DATA -->`
      );
      
      // Write the updated HTML back
      await this.file.writeExistingFile(folderPath, 'index.html', updatedHtml);
      
    } catch (error) {
      console.error('Error updating HTML with form data:', error);
    }
  }

  /**
   * Opens the HTML file using InAppBrowser based on platform
   */
  async openFileInBrowser(filePath: string , form : FORM_HEADER) {
    console.log('the form is ' , form);
    this.lidOfLastOpenedForm = form.LID;
    let actualData = form.DATA;
    const that = this;
    
    if (this.platform.is('ios')) {
      // For iOS, use InAppBrowser with the original file:// URL
      console.log("Opening file in InAppBrowser for iOS:", filePath);
      
      this.myBrowser = this.iab.create(filePath, '_blank', {
        location: 'no',
        clearcache: 'yes',
        clearsessioncache: 'yes',
        fullscreen: 'yes',
        hardwareback: 'no',
        toolbar: 'no'
      });

      // LOADSTOP EVENT
      this.myBrowser.on('loadstop').subscribe((response: any) => {
        console.log('File loaded successfully in InAppBrowser');
        this.unviredSdk.logInfo('FormsPage', 'openFileInBrowser', 'loadStop Event called with ' + response.url);
        this.myBrowser.show();

        const val = (response.url || '').toLowerCase();
        if (val.includes('#')) {
          // Do nothing if hash in URL
          return;
        }

        setTimeout(() => {
          // Get prefilled data from store and execute form initialization
          this.store.select(selectPrefilledData).pipe(take(1)).subscribe(prefilledData => {
            // Extract logo separately like other services do
            const logo = prefilledData?.LOGO || '';
            const formDataWithoutLogo = { ...prefilledData };
            delete formDataWithoutLogo?.LOGO;

            this.myBrowser.executeScript({
              code: 'loadDefaultValues(' + JSON.stringify(formDataWithoutLogo || {}) + ')'
            });

              if (!(actualData == null || actualData == "null")) {
            this.myBrowser.executeScript({
              code: "loadPreviousValues(" + actualData + ")"
            })
          }

            // Set company logo separately
            this.myBrowser.executeScript({
              code: 'var companyLogo = \'' + logo + '\';'
            });

            const formStatus = 'OPEN';
            const status = {
              FormStatus: form.FORM_STATUS,
              IsSTMR: false
            };
            
            this.myBrowser.executeScript({
              code: 'setStatus(' + JSON.stringify(status) + ')'
            });
          });
        }, 1200);
      });

      // LOADERROR EVENT
      this.myBrowser.on('loaderror').subscribe((error) => {
        console.error('Error loading file in InAppBrowser:', error);
        this.unviredSdk.logError('FormsPage', 'openFileInBrowser', `Error loading file: ${JSON.stringify(error)}`);
      });

      // EXIT EVENT
      this.myBrowser.on('exit').subscribe((response: any) => {
        console.log('Browser exited:', response);
      });

      // LOADSTART EVENT - Handle form interactions
      // this.myBrowser.on('loadstart').subscribe((response: any) => {
      //   console.log('response is ' , response);
      //   const val = (response.url || '').toLowerCase();
      //   console.log('Load Start Response URL:', response.url);

      //   // Handle form submission/save actions
      //   if (val.includes('submitbuttonclicked') || 
      //       val.includes('savebuttonclicked') || 
      //       val.includes('saveandexitbuttonclicked')) {
          
      //     const isSubmit = val.includes('submitbuttonclicked');
      //     const closeBrowser = !val.includes('savebuttonclicked');

      //     // Get form data from HTML
      //     this.myBrowser.executeScript({ code: 'generateJSON()' })
      //       .then((result: any) => {
      //         if (result && result.length > 0) {
      //           console.log('Form data retrieved:', result[0]);
      //           // Here you would save the form data
      //           // this.saveFormData(result[0], isSubmit);
                
      //           if (closeBrowser) {
      //             this.myBrowser.close();
      //           }
      //         }
      //       })
      //       .catch((error: any) => {
      //         console.error('Error getting form data:', error);
      //         this.myBrowser.close();
      //       });
      //   }
      //   // Handle back button
      //   else if (val.includes('backbuttonclicked')) {
      //     this.myBrowser.close();
      //   }
      //   // Handle print button
      //   else if (val.includes('printbuttonclicked')) {
      //     this.myBrowser.executeScript({ code: 'generateJSON()' })
      //       .then((result: any) => {
      //         if (result && result.length > 0) {
      //           console.log('Print data:', result[0]);
      //           // Handle print functionality
      //         }
      //       });
      //   }
      // });
          this.myBrowser.on("loadstart").subscribe(response => {
      this.ngZone.run(() => {
        console.log('response is ' , response);
        console.log("Load Start Response URL: " + response.url)

        let val = (response.url).toLowerCase();
        console.log("FormsPage", "viewForm", "value is " + val)
        if (val.includes(("submitButtonClicked").toLowerCase()) ||
          val.includes(("saveButtonClicked").toLowerCase()) ||
          val.includes(("saveAndExitButtonClicked").toLowerCase())) {
            
          var isSubmit = val.includes(("submitButtonClicked").toLowerCase());
            console.log('IS SUBMIT' , isSubmit);
          // Use the Latest Form Object for Updating Data.
          // Because the Latest Form Object will contain any previously saved data.
          this.unviredSdk.logInfo("FormsPage", "viewForm", "Form LID to Update:" + this.lidOfLastOpenedForm)
          // Get the latest form object from the observable
          this.forms$.pipe(take(1)).subscribe(forms => {
            form = forms.find(f => f.LID == that.lidOfLastOpenedForm) || form;
          });
         
          // Do not close browser if "saveButtonClicked"
          var formSubmit = AppConstants.BOOL_TRUE, // val.includes(("saveButtonClicked").toLowerCase()) ? AppConstant.BOOL_FALSE : AppConstant.BOOL_TRUE,
            closeBrowser = val.includes(("saveButtonClicked").toLowerCase()) ? AppConstants.BOOL_FALSE : AppConstants.BOOL_TRUE;

          // if the form is in Outbox, submit the form to server.
          if (val.includes(("saveButtonClicked").toLowerCase())) {
            if (form.SYNC_STATUS == AppConstants.SYNC_STATUS.QUEUED) {
              formSubmit = true
            }
            else {
              formSubmit = false
            }
          }

          // If Submit Form - Save Data and Submit form to server
          // If Save Form - Only Save Data
          // Save And Exit Form - Save Data and Submit form to server (partial)
          this.formUtilityService.getFormDataFromHTML(this.myBrowser, async (result: any) => {
              console.log('result is in getFormDATAhTML ' , result);
            this.unviredSdk.logInfo("FormsPage", "viewForm", "Checking if form is in sent items.");

            // 1. If in sent item
            let resultIsInSentItem  = await this.unviredSdk.isInSentItem(form.LID);
            this.unviredSdk.isInSentItem(form.LID).then((resultInSent: any) => {
              
              if (resultInSent &&  resultInSent == AppConstants.BOOL_TRUE) {

                this.unviredSdk.logInfo("FormsPage", "viewForm", "Form is already sent to server. Discarding form changes.");

                // Discard the latest changes with an error to the user indicating that reconciliation in process
                this.myBrowser.close();
                this.lidOfLastOpenedForm = '';

               this.unviredSdk.logInfo("FormPage", val, "UNLOCKING FORM: " + form.LID)

               this.unviredSdk.getMessages();
                let alert = this.alertController.create({
                  "header": "Previous request still being reconciled!",
                  "message": "Your previous change to this form is being reconciled. Do you want to discard your changes and let the reconciliation finish or continue editing the form and attempt to save later?",
                  "cssClass": "data-save-modal",
                  "buttons": [
                    {
                      "text": "Continue Editing",
                      "role": "",
                      handler: () => {
                        // Re-open the form with changes
                        try {
                          var tmpForm = JSON.parse(JSON.stringify(form));
                          tmpForm.DATA = JSON.parse(result[0]);
                          console.log('tempForm data is ' , tmpForm.DATA);
                          // that.open(tmpForm);
                        } catch (e) {
                          // that.alertController.showAlert("Error parsing data", "Click on the forms listing to re-open the form.");
                        };
                      }
                    },
                    {
                      "text": "Discard Changes",
                      "role": "cancel",
                      handler: () => {
                        // Stay in same(Forms) screen
                      }
                    }
                  ]
                })
                // await alert.present();

              }
              else if ( resultInSent == AppConstants.BOOL_FALSE) {

                this.unviredSdk.logInfo("FormsPage", "viewForm", "Form not in sent items. Saving form in Database.");

                // Save form data to database
                this.store.select(selectPrefilledData).pipe(take(1)).subscribe(prefilledData => {
                  this.updateFormsArray(form, result[0], prefilledData?.USER_ID || '', formSubmit, isSubmit);
                });

                if (closeBrowser) {
                  this.myBrowser.close();
                  this.lidOfLastOpenedForm = ''
                 this.unviredSdk.logInfo("FormPage", val, "UNLOCKING FORM: " + form.LID)
                }
              }
            })
          })

        } else if (val.includes(("backButtonClicked").toLowerCase())) {
          // Go Back - close browser
          this.unviredSdk.logInfo("FormsPage", "viewForm", "LID of Last selected Form: " + this.lidOfLastOpenedForm)
          this.myBrowser.close();
          this.lidOfLastOpenedForm = ''

          this.unviredSdk.logInfo("FormPage", val, "UNLOCKING FORM: " + form.LID)
          // ump.sync.unlockDataSender(() => {
          //   this.unviredSdk.logInfo("FormsPage", val, "Unlocked Data sender")
          // })

        }
        else if (val.includes(('printButtonClicked').toLowerCase())) {

          // Print Form
          // this.formUtility.getFormDataFromHTML(this.alertService, this.myBrowser, (result) => {
          //   this.checkDirectory(attachmentPath, form, zipFilePath, result[0], prefillData.LOGO);
          // })
        }
        else if (val.includes(('documentLinkButtonClicked').toLowerCase())) {
          var documentFilePath = response.url.substring(response.url.indexOf('documentLinkButtonClicked_') + 26)
          documentFilePath = documentFilePath.substring(0, documentFilePath.indexOf('.pdf') + 4)
          documentFilePath = decodeURI(documentFilePath)
          this.unviredSdk.logInfo("FormsPage", 'documentLinkButtonClicked Event Handling', "Trying to open the file: " + documentFilePath)

          // this.displayDocumentService.displayDocument(documentFilePath)
        }
      })
    })


    } else {
      // Android still works fine with InAppBrowser
      const urlEncodedPath = encodeURI(filePath);
      this.iab.create(urlEncodedPath, '_blank', { location: 'no' });
    }
  }


   //Update form view
  async updateFormsArray(form: FORM_HEADER, formData: string, loggedInUserId: string, submitForm: boolean, isSubmit?: boolean, isQueued?: boolean) {
    // Form Header with FORM_STATUS
    console.log('updateFormsArray' , form , formData , loggedInUserId , submitForm , isSubmit , isQueued);
    let that = this,
      status = "",
      dateComp = moment.utc().unix(),
      tmpHeader = JSON.parse(JSON.stringify(form));

    if (isSubmit) status = AppConstants.VAL_FORM_STATUS.SUBM;
    else status = AppConstants.VAL_FORM_STATUS.INPR;

    tmpHeader.FORM_STATUS = status;
    tmpHeader.DATE_COMP = dateComp;
    tmpHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;

    // Update Operator Info
    var operatorInfo = ''
    let data = JSON.parse(formData)
    for (const key in data) {
      var subJSON = data[key]
      for (const key2 in subJSON) {
        if (key2 == 'Operator') {
          operatorInfo = subJSON[key2]
          break;
        }
      }
    }
    tmpHeader.OPERATOR = operatorInfo.replace("'", "''")

    // Do not update Sync Status if already Queued
    if (!isQueued) {
      tmpHeader.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    }

    tmpHeader.LAST_SYNC_USER = loggedInUserId;
    tmpHeader.SUBM_BY = loggedInUserId;

    this.busyIndicatorService.displayBusyIndicator("Updating form status ...");

    let timezone = this.utilityService.getTimezone()

    console.log('tmp header is ' , tmpHeader.OPERATOR , tmpHeader.FORM_ID , dateComp , status);
    let query = `UPDATE FORM_HEADER SET FORM_STATUS ='${status}', DATE_COMP ='${dateComp}', OBJECT_STATUS='${AppConstants.OBJECT_STATUS.MODIFY}', LAST_SYNC_USER = '${loggedInUserId}', SUBM_BY = '${loggedInUserId}', TIME_ZONE = '${timezone}', OPERATOR = '${tmpHeader.OPERATOR}' WHERE FORM_ID = '${tmpHeader.FORM_ID}'`;
   console.log('query isss' , query);
    await this.unviredSdk.dbExecuteStatement(query).then(async (result: any) => {
      console.log('result after update query is ' , result);

      if (result.type === ResultType.success) {
        // Form Data
        let formsDataObj: FORM_DATA = <FORM_DATA>{};
        formsDataObj.FORM_ID = tmpHeader.FORM_ID;
        formsDataObj.DATA = formData;
        console.log('formsDataObj' , formsDataObj);
        let parsedFormData = JSON.parse(formData);
if (parsedFormData.LID) {
  formsDataObj.LID = parsedFormData.LID;
  formsDataObj.FID = parsedFormData.DATA_FID;
  formsDataObj.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
} else if (tmpHeader.LID) {
  formsDataObj.FID = tmpHeader.LID;
  formsDataObj.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
}
        if (tmpHeader.LID) {
          formsDataObj.FID = tmpHeader.LID;
          formsDataObj.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
        }

        // Do not update Sync Status if already Queued
        if (isQueued) {
          formsDataObj.SYNC_STATUS = AppConstants.SYNC_STATUS.QUEUED;
        } else {
          formsDataObj.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
        }
        console.log('formsDataObj is ' , formsDataObj.DATA);

              let updateFormDataQuery = `UPDATE FORM_DATA SET DATA = '${formsDataObj.DATA}', OBJECT_STATUS = '${AppConstants.OBJECT_STATUS.MODIFY}', P_MODE = 'M' WHERE FORM_ID = '${formsDataObj.FORM_ID}'`;
        // console.log('updateFormDataQuery is ' , updateFormDataQuery);
        let updateResult = await this.unviredSdk.dbExecuteStatement(updateFormDataQuery);
        // console.log('updateResult is ' , updateResult);
        if (updateResult.type === ResultType.success) {
          // console.log('Form data updated successfully');
           let selectQuery = `SELECT * FROM FORM_DATA WHERE FORM_ID = '${formsDataObj.FORM_ID}'`;
          let selectResult = await this.unviredSdk.dbExecuteStatement(selectQuery);
          // console.log('selectResult is ' , selectResult);
           if (isSubmit) {
              // Create FORM_ACTION for the form
              let formActionsObj: any = {};
              formActionsObj.FORM_ID = tmpHeader.FORM_ID;
              formActionsObj.ACTION_CODE = AppConstants.ACTION_CODE?.COMPLETE;
              formActionsObj.FID = tmpHeader.LID;
              formActionsObj.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;


              this.unviredSdk.dbInsertOrUpdate(AppConstants.TABLE_FORM_ACTION, formActionsObj, AppConstants.BOOL_FALSE).then((result: any) => {
                if (result.type === ResultType.success) {

                  if (submitForm) {
                    tmpHeader = this.deleteFormPageFields(tmpHeader);
                    this.callCreateForm(tmpHeader);
                  } else {
                    this.reloadForm(formsDataObj.FID)
                    that.busyIndicatorService.hideBusyIndicator();
                  }

                } else {
                  that.busyIndicatorService.hideBusyIndicator();
                  that.unviredSdk.logError("FormsPage", "updateFormsArray", "Inserting Form Action into database failed: " + result.message + " " + result.error);
                }
              });
            } else {
              if (submitForm) {
                tmpHeader = this.deleteFormPageFields(tmpHeader);
                this.callCreateForm(tmpHeader);
              } else {
                this.reloadForm(formsDataObj.FID)
                that.busyIndicatorService.hideBusyIndicator();
              }
            }
        }
          else {
            that.busyIndicatorService.hideBusyIndicator();
            that.unviredSdk.logError("FormsPage", "updateFormsArray", "Inserting/Updating Form Data into database failed: " + result.message + " " + result.error);
          }
      }
      else {
        that.busyIndicatorService.hideBusyIndicator();
        that.unviredSdk.logError("FormsPage", "updateFormsArray", "Inserting/Updating Form Header into database failed: " + result.message + " " + result.error);
      }
    })
  }


    // Delete all additional fields from the given object
  deleteFormPageFields(tmpHeader: any) {
    try {
      var delHeader = tmpHeader;
      if (delHeader.DATA) delete delHeader.DATA;
      if (delHeader.formStatus) delete delHeader.formStatus;
      if (delHeader.syncStatus) delete delHeader.syncStatus;
      if (delHeader.isSelected) delete delHeader.isSelected;
      if (delHeader.CAT_ID) delete delHeader.CAT_ID;
      if (delHeader.CATEGORY_DESC) delete delHeader.CATEGORY_DESC;
      if (delHeader.TMPLT_ID) delete delHeader.TMPLT_ID;
      if (delHeader.TEMPLATE_DESC) delete delHeader.TEMPLATE_DESC;
      if (delHeader.PUBLISHED_ON) delete delHeader.PUBLISHED_ON;
      if (delHeader.DATA) delete delHeader.DATA;
      if (delHeader.DATA_LID) delete delHeader.DATA_LID;
      if (delHeader.DATA_FID) delete delHeader.DATA_FID;
    } catch (e) {
      this.unviredSdk.logError("FormsPage", "deleteFormPageFields", "Deleting additional fields from given custom FORM object failed.");
    }
    return delHeader;
  }

    async callCreateForm(formHeader: FORM_HEADER) {
      console.log('the form header in callcreateform is ' , formHeader);
    let that = this,
      inputHeader = {
        "FORM_HEADER": formHeader
      };

    // Queue the latest form to the server
    this.unviredSdk.logInfo("FormsPage", "callCreateForm", "Sending form to server in async mode.");
  //  await this.unviredSdk.syncBackground(RequestType.RQST, inputHeader, "", AppConstants.FORMS_PA_FORM_SUBMIT, "FORM", formHeader.LID, AppConstants.BOOL_FALSE).subscribe( (result) => {

      that.busyIndicatorService.hideBusyIndicator();
      that.reloadForm(formHeader.LID)

    //   if (result.type == ResultType.success) {
    //    this.unviredSdk.logInfo("FormsPage", "callCreateForm", "Form submitted successfully.");
    //     // that.updateSyncItemsCount()
    //   } else {
    //     // that.alertController.("Error", "Form submission failed.");
    //    this.unviredSdk.logError("FormsPage", "callCreateForm", "Form submission failed: " + result.message + " " + result.error);
    //   }
    // }
  }


    async reloadForm(formLid: any) {
      console.log('formLid is ' , formLid);
    // In Category mode, the screen does not refresh.
    // Therefore we are reloading the complete screeb.
    if (this.categoryToggle) {
      this.store.dispatch(RigActions.loadAllFormsFromDb());

      return
    }
this.store.dispatch(RigActions.loadAllFormsFromDb());
      this.forms$ = this.store.select(selectAllForms);
    let that = this
    let  query = this.dataService.returnFormsPageQuery(formLid);
   this.unviredSdk.logInfo("FormsPage", "reloadForm", "Query to reload specific Form: " )
    var results: any = []

    let reloadForm = await this.unviredSdk.dbExecuteStatement(query)

    if(reloadForm.type === ResultType.success){
      if(reloadForm.data.length > 0){
        results = reloadForm.data;
        console.log('result in reload form is ' , results);

                results = reloadForm.data;
        results.forEach((element: any) => {
          if (element.FORM_STATUS) {
            try {
              element["formStatus"] = this.utilityService.getFormStatusObj(element.FORM_STATUS);
              var tmp = this.utilityService.getSyncStatusObj(element.SYNC_STATUS);
              console.log('tmp is in forms page ' , tmp);
              if (tmp["descr"] && tmp["color"]) element["syncStatus"] = tmp;
            } catch (e) {
              this.unviredSdk.logError("FormsPage", "reloadForm", "Determining status error : " + JSON.stringify(e));
            }
          }
          if (element.DATA) {
            try {
              element.DATA = JSON.parse(element.DATA);
              console.log('element.DATA is ' , element.DATA);
            } catch (e) {
              this.unviredSdk.logError("FormsPage", "reloadForm", "Parsing FORM_DATA error : " + JSON.stringify(e));
            }
          }
        });
      }

     let formPageQueryResult = await this.unviredSdk.dbExecuteStatement(query)
      if(formPageQueryResult.type === ResultType.success){
        if(formPageQueryResult.data.length > 0){
          results = formPageQueryResult.data;
          results.forEach((element: any) => {
            if (element.FORM_STATUS) {
              try {
                element["formStatus"] = this.utilityService.getFormStatusObj(element.FORM_STATUS);
                var tmp = this.utilityService.getSyncStatusObj(element.SYNC_STATUS);
                if (tmp["descr"] && tmp["color"]) element["syncStatus"] = tmp;
              } catch (e) {
                this.unviredSdk.logError("FormsPage", "reloadForm", "Determining status error : " + JSON.stringify(e));
              }
            }
            if (element.DATA) {
              try {
                element.DATA = JSON.parse(element.DATA);
              } catch (e) {
                this.unviredSdk.logError("FormsPage", "reloadForm", "Parsing FORM_DATA error : " + JSON.stringify(e));
              }
            }
          });
      } else {
        results = [];
        this.unviredSdk.logError("Forms", "reloadForm", "Did not get a Form Record for Form ID: " + formLid);
        return;
      }
    }

       //     let formIndex = that.forms.findIndex((formHeader) => {
    //       if (formHeader.LID == formLid) {
    //         return true
    //       }
    //       return false
    //     })

    //     let newFormsIndex = that.newFormsArray.findIndex((formHeader) => {
    //       if (formHeader.LID == formLid) {
    //         return true
    //       }
    //       return false
    //     })

    //     // Set the |INDEX| of the form in the list.
    //     results[0].INDEX = formIndex

    //     ump.log.i("Forms", "reloadForm", "Reloading the list at the index: " + newFormsIndex + " When Form with this ID updated:" + formLid)

    //     that.ngZone.run(() => {
    //       that.forms[formIndex] = results[0];
    //       that.newFormsArray[newFormsIndex] = results[0];
    //       that.newCategories = that.newCategories;
    //     })

    //   } else {
    //     ump.log.e("FormsPage", "reloadForm", "Error while fetching Forms Page Data from DB : " + JSON.stringify(result.error));
    //     that.alertService.showAlert("Error", JSON.stringify(result.error));
    //   }
    // ump.db.executeStatement(query, (result) => {
    //   if (result.type == ump.resultType.success) {
    //     let count = result.data.length;
    //     ump.log.i("FormsPage", "reloadForm", "Forms Count: " + count);
    //     if (count > 0) {
    //       results = result.data;
    //       results.forEach(element => {
    //         if (element.FORM_STATUS) {
    //           try {
    //             element["formStatus"] = this.utilityService.getFormStatusObj(element.FORM_STATUS);
    //             var tmp = this.utilityService.getSyncStatusObj(element.SYNC_STATUS);
    //             if (tmp["descr"] && tmp["color"]) element["syncStatus"] = tmp;
    //           } catch (e) {
    //             ump.log.e("FormsPage", "reloadForm", "Determining status error : " + JSON.stringify(e));
    //           }
    //         }
    //         if (element.DATA) {
    //           try {
    //             element.DATA = JSON.parse(element.DATA);
    //           } catch (e) {
    //             ump.log.e("FormsPage", "reloadForm", "Parsing FORM_DATA error : " + JSON.stringify(e));
    //           }
    //         }
    //       });
    //     }
    //     else {
    //       results = [];
    //       ump.log.e("Forms", "reloadForm", "Did not get a Form Record for Form ID: " + formLid);
    //       return;
    //     }

    //     // Determine the Index in |this.forms| & |this.newFormsArray|
    //     let formIndex = that.forms.findIndex((formHeader) => {
    //       if (formHeader.LID == formLid) {
    //         return true
    //       }
    //       return false
    //     })

    //     let newFormsIndex = that.newFormsArray.findIndex((formHeader) => {
    //       if (formHeader.LID == formLid) {
    //         return true
    //       }
    //       return false
    //     })

    //     // Set the |INDEX| of the form in the list.
    //     results[0].INDEX = formIndex

    //     ump.log.i("Forms", "reloadForm", "Reloading the list at the index: " + newFormsIndex + " When Form with this ID updated:" + formLid)

    //     that.ngZone.run(() => {
    //       that.forms[formIndex] = results[0];
    //       that.newFormsArray[newFormsIndex] = results[0];
    //       that.newCategories = that.newCategories;
    //     })

    //   } else {
    //     ump.log.e("FormsPage", "reloadForm", "Error while fetching Forms Page Data from DB : " + JSON.stringify(result.error));
    //     that.alertService.showAlert("Error", JSON.stringify(result.error));
    //   }
    // }, "FORM_PAGE_DATA")
  }

    }


    async checkForHtmlFileInFolder(attachmentPath: string, folderName: string, form: FORM_HEADER){
            try {
          const folderPath = `${attachmentPath}${folderName}/`;
          console.log('Checking for index.html in path:', folderPath);
          
          await this.file.checkFile(folderPath, 'index.html');
          console.log('index.html file exists in folder.');

          // Update the HTML file with form data and logo before opening
          await this.updateFormDataInHTML(folderPath, form);
          
          const indexHtmlPath = `${folderPath}index.html`;
          this.openFileInBrowser(indexHtmlPath , form);

        } catch (fileError) {
          console.log('index.html not found, checking other files in folder...');
          try {
            const entries = await this.file.listDir(attachmentPath, folderName);
            console.log('Files in folder:', entries.map(entry => entry.name));
          } catch (listError) {
            console.log('Error listing directory contents:', listError);
          }
        }
    }

}
